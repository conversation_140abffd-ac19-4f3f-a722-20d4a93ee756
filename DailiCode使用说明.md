# DailiCode 使用说明

## 📖 简介

DailiCode 是一个开源的 AI Agent CLI 工具，支持多种大语言模型，专为编程和开发任务设计。本工具是 Gemini CLI 的分支版本，已配置使用 OpenRouter API 来访问多种先进的 AI 模型。

## 🚀 快速开始

### 方法一：快捷命令（推荐）

打开终端，直接输入以下任一命令：

```bash
# 默认模型 (GPT-4o-mini)
dlc-ai

# 或者使用具体模型
dlc-mini      # GPT-4o-mini (推荐 - 便宜快速)
dlc-4o        # GPT-4o (更强大)
dlc-claude    # Claude 3.5 Sonnet (编程专家)
dlc-gemini    # Gemini 2.0 Flash (Google最新)
```

### 方法二：交互式模型选择

```bash
~/dlc-select.sh
```

运行后会显示模型选择菜单，按数字选择即可。

### 方法三：在配置目录中使用

```bash
cd /Users/<USER>/DailiCode/dailicode-local
dlc
```

## 🤖 支持的模型

| 命令 | 模型 | 特点 | 适用场景 | 价格 |
|------|------|------|----------|------|
| `dlc-mini` | GPT-4o-mini | 快速、便宜 | 日常编程、快速问答 | 💰 |
| `dlc-4o` | GPT-4o | 更强大、更准确 | 复杂编程、深度分析 | 💰💰 |
| `dlc-claude` | Claude 3.5 Sonnet | 编程专家 | 代码重构、架构设计 | 💰💰 |
| `dlc-gemini` | Gemini 2.0 Flash | Google最新 | 多模态任务、创新功能 | 💰💰 |

## 🎯 主要功能

### 1. 代码分析和生成
- 代码审查和优化建议
- 自动生成代码片段
- 代码重构和改进
- Bug 修复建议

### 2. 文件操作
- 读取和分析项目文件
- 批量文件处理
- 代码搜索和替换

### 3. MCP 服务器支持
- 集成多个 MCP (Model Context Protocol) 服务器
- 扩展工具调用能力
- 按 `Ctrl+T` 查看可用的 MCP 服务器

### 4. 多模态支持
- 文本处理
- 图像分析（部分模型支持）
- 文件上传和分析

## 🔧 界面操作

### 基本操作
- **输入消息**：直接输入文本或使用 `@path/to/file` 引用文件
- **退出程序**：输入 `/quit` 或按 `Ctrl+C`
- **清屏**：输入 `/clear`

### 快捷键
- **Ctrl+M / Cmd+M**：切换模型
- **Ctrl+T**：查看 MCP 服务器
- **ESC**：取消当前操作
- **↑/↓**：浏览历史消息

### 文件引用
```bash
# 分析单个文件
@src/main.py

# 分析多个文件
@src/main.py @src/utils.py

# 分析整个目录
@src/
```

## 💡 使用技巧

### 1. 选择合适的模型
- **日常编程**：使用 `dlc-mini`（GPT-4o-mini）
- **复杂项目**：使用 `dlc-4o` 或 `dlc-claude`
- **多模态任务**：使用 `dlc-gemini`

### 2. 高效的提问方式
```bash
# 好的提问示例
请帮我优化这个函数的性能 @src/slow_function.py

# 分析整个项目结构
请分析这个项目的架构 @src/

# 代码审查
请审查这些文件中的潜在问题 @src/*.py
```

### 3. 利用上下文
- DailiCode 会记住对话历史
- 可以进行多轮对话来深入讨论问题
- 使用具体的文件路径获得更准确的建议

## 🔄 模型切换

### 在界面内切换
运行 DailiCode 后，按 `Ctrl+M` 或 `Cmd+M` 打开模型选择菜单。

### 命令行切换
退出当前会话，使用不同的启动命令：
```bash
# 从 mini 切换到 Claude
# 先退出当前会话 (/quit)
# 然后运行
dlc-claude
```

## 📁 配置文件位置

- **环境变量配置**：`~/.zshrc`
- **本地配置**：`/Users/<USER>/DailiCode/dailicode-local/.env`
- **快捷脚本**：`~/dlc-select.sh`

## 🛠️ 故障排除

### 常见问题

1. **命令未找到**
   ```bash
   # 重新加载配置
   source ~/.zshrc
   ```

2. **API 错误**
   - 检查网络连接
   - 确认 API 密钥有效
   - 尝试切换到其他模型

3. **工具调用失败**
   - 确保使用支持工具调用的模型
   - 避免使用不支持的模型（如某些 Qwen 版本）

### 重新安装
如果遇到严重问题，可以重新安装：
```bash
cd /Users/<USER>/DailiCode/dailicode-local
npm install
npm link
```

## 📞 获取帮助

- **GitHub 仓库**：https://github.com/nearmetips/DailiCode
- **在线文档**：查看项目 README
- **社区支持**：GitHub Issues

## 🎉 开始使用

现在您可以打开终端，输入 `dlc-ai` 开始您的 AI 编程之旅！

---

*最后更新：2025年1月*
