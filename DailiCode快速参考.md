# DailiCode 快速参考卡片

## 🚀 启动命令

```bash
dlc-ai        # 默认 (GPT-4o-mini)
dlc-mini      # GPT-4o-mini (便宜快速)
dlc-4o        # GPT-4o (更强大)
dlc-claude    # Claude 3.5 Sonnet (编程专家)
dlc-gemini    # Gemini 2.0 Flash (Google最新)
```

## 🎯 交互式选择器

```bash
~/dlc-select.sh
```

## ⌨️ 界面快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl+M` / `Cmd+M` | 切换模型 |
| `Ctrl+T` | 查看 MCP 服务器 |
| `ESC` | 取消操作 |
| `↑/↓` | 浏览历史 |

## 📁 文件引用

```bash
@file.py           # 单个文件
@src/main.py       # 指定路径
@src/*.py          # 通配符
@src/              # 整个目录
```

## 💬 常用命令

```bash
/quit              # 退出
/clear             # 清屏
```

## 🔧 故障排除

```bash
source ~/.zshrc    # 重新加载配置
```

## 📍 配置文件

- `~/.zshrc` - 环境变量
- `~/dlc-select.sh` - 模型选择器
- `/Users/<USER>/DailiCode/dailicode-local/.env` - 本地配置

---
**提示**：首次使用建议阅读完整的 `DailiCode使用说明.md`
