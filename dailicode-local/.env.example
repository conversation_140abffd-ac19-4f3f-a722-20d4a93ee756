
# 获取 API Key: https://openrouter.ai/keys
USE_CUSTOM_LLM=true
CUSTOM_LLM_PROVIDER="openai"
CUSTOM_LLM_API_KEY="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"
CUSTOM_LLM_ENDPOINT="https://openrouter.ai/api/v1"
CUSTOM_LLM_MODEL_NAME="qwen/qwen-2.5-coder-32b-instruct"

# 可选参数
CUSTOM_LLM_TEMPERATURE=0.7
CUSTOM_LLM_MAX_TOKENS=8192
CUSTOM_LLM_TOP_P=1

# 备用配置 - Gemini API Key
# GEMINI_API_KEY="your-Gemini-api-key-here"

# 备用配置 - Google OAuth
# GOOGLE_CLIENT_ID="681255809395-oo8ft2oprdrnp9e3aqf6av3hmdib135j.apps.googleusercontent.com"
# GOOGLE_CLIENT_SECRET="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"

# 备用配置 - DailiCode OAuth
# DAILICODE_CLIENT_ID="your-dailicode-client-id-here"
# DAILICODE_CLIENT_SECRET="your-dailicode-client-secret-here"

