{"version": 3, "file": "to-file.mjs", "sourceRoot": "", "sources": ["../src/internal/to-file.ts"], "names": [], "mappings": "OAAO,EAAY,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE;OAEhD,EAAE,gBAAgB,EAAE;AAmB3B;;GAEG;AACH,MAAM,UAAU,GAAG,CAAC,KAAU,EAA+D,EAAE,CAC7F,KAAK,IAAI,IAAI;IACb,OAAO,KAAK,KAAK,QAAQ;IACzB,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ;IAC9B,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ;IAC9B,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU;IAChC,OAAO,KAAK,CAAC,KAAK,KAAK,UAAU;IACjC,OAAO,KAAK,CAAC,WAAW,KAAK,UAAU,CAAC;AAY1C;;GAEG;AACH,MAAM,UAAU,GAAG,CAAC,KAAU,EAA+D,EAAE,CAC7F,KAAK,IAAI,IAAI;IACb,OAAO,KAAK,KAAK,QAAQ;IACzB,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ;IAC9B,OAAO,KAAK,CAAC,YAAY,KAAK,QAAQ;IACtC,UAAU,CAAC,KAAK,CAAC,CAAC;AAUpB,MAAM,cAAc,GAAG,CAAC,KAAU,EAAyB,EAAE,CAC3D,KAAK,IAAI,IAAI;IACb,OAAO,KAAK,KAAK,QAAQ;IACzB,OAAO,KAAK,CAAC,GAAG,KAAK,QAAQ;IAC7B,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AAQnC;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,MAAM,CAC1B,KAA6C,EAC7C,IAAgC,EAChC,OAAqC;IAErC,gBAAgB,EAAE,CAAC;IAEnB,iCAAiC;IACjC,KAAK,GAAG,MAAM,KAAK,CAAC;IAEpB,4DAA4D;IAC5D,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QACtB,IAAI,KAAK,YAAY,IAAI,EAAE,CAAC;YAC1B,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,QAAQ,CAAC,CAAC,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1B,MAAM,IAAI,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC;QAChC,IAAI,KAAJ,IAAI,GAAK,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAC;QAE1D,OAAO,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,KAAK,CAAC,CAAC;IAEpC,IAAI,KAAJ,IAAI,GAAK,OAAO,CAAC,KAAK,CAAC,EAAC;IAExB,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC;QACnB,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3F,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,IAAI,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IAED,OAAO,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACxC,CAAC;AAED,KAAK,UAAU,QAAQ,CAAC,KAAiD;IACvE,IAAI,KAAK,GAAoB,EAAE,CAAC;IAChC,IACE,OAAO,KAAK,KAAK,QAAQ;QACzB,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,oCAAoC;QACjE,KAAK,YAAY,WAAW,EAC5B,CAAC;QACD,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpB,CAAC;SAAM,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7B,KAAK,CAAC,IAAI,CAAC,KAAK,YAAY,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;IACxE,CAAC;SAAM,IACL,eAAe,CAAC,KAAK,CAAC,CAAC,0CAA0C;MACjE,CAAC;QACD,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,KAAK,EAAE,CAAC;YAChC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,KAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,6BAA6B;QACvF,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,WAAW,GAAG,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC;QAC7C,MAAM,IAAI,KAAK,CACb,yBAAyB,OAAO,KAAK,GACnC,WAAW,CAAC,CAAC,CAAC,kBAAkB,WAAW,EAAE,CAAC,CAAC,CAAC,EAClD,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE,CAC1B,CAAC;IACJ,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,aAAa,CAAC,KAAc;IACnC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI;QAAE,OAAO,EAAE,CAAC;IAC3D,MAAM,KAAK,GAAG,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAChD,OAAO,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;AAC/D,CAAC"}