{"version": 3, "file": "default.js", "sourceRoot": "", "sources": ["../../../../src/ui/themes/default.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AAE9C,MAAM,CAAC,MAAM,WAAW,GAAU,IAAI,KAAK,CACzC,SAAS,EACT,MAAM,EACN;IACE,IAAI,EAAE;QACJ,OAAO,EAAE,OAAO;QAChB,SAAS,EAAE,MAAM;QACjB,OAAO,EAAE,OAAO;QAChB,UAAU,EAAE,SAAS,CAAC,UAAU;QAChC,KAAK,EAAE,SAAS,CAAC,UAAU;KAC5B;IACD,cAAc,EAAE;QACd,KAAK,EAAE,SAAS,CAAC,UAAU;KAC5B;IACD,cAAc,EAAE;QACd,KAAK,EAAE,SAAS,CAAC,UAAU;KAC5B;IACD,aAAa,EAAE;QACb,KAAK,EAAE,SAAS,CAAC,UAAU;KAC5B;IACD,WAAW,EAAE;QACX,KAAK,EAAE,SAAS,CAAC,UAAU;KAC5B;IACD,WAAW,EAAE;QACX,KAAK,EAAE,SAAS,CAAC,UAAU;QAC3B,cAAc,EAAE,WAAW;KAC5B;IACD,eAAe,EAAE;QACf,KAAK,EAAE,SAAS,CAAC,UAAU;KAC5B;IACD,WAAW,EAAE;QACX,KAAK,EAAE,SAAS,CAAC,UAAU;KAC5B;IACD,aAAa,EAAE;QACb,KAAK,EAAE,SAAS,CAAC,WAAW;KAC7B;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,SAAS,CAAC,WAAW;KAC7B;IACD,aAAa,EAAE;QACb,KAAK,EAAE,SAAS,CAAC,YAAY;KAC9B;IACD,kBAAkB,EAAE;QAClB,KAAK,EAAE,SAAS,CAAC,YAAY;KAC9B;IACD,aAAa,EAAE;QACb,KAAK,EAAE,SAAS,CAAC,SAAS;KAC3B;IACD,mBAAmB,EAAE;QACnB,KAAK,EAAE,SAAS,CAAC,SAAS;KAC3B;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,SAAS,CAAC,UAAU;KAC5B;IACD,eAAe,EAAE;QACf,KAAK,EAAE,SAAS,CAAC,UAAU;KAC5B;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,SAAS,CAAC,UAAU;KAC5B;IACD,aAAa,EAAE;QACb,KAAK,EAAE,SAAS,CAAC,UAAU;KAC5B;IACD,cAAc,EAAE;QACd,KAAK,EAAE,SAAS,CAAC,UAAU;KAC5B;IACD,cAAc,EAAE;QACd,KAAK,EAAE,SAAS,CAAC,OAAO;QACxB,SAAS,EAAE,QAAQ;KACpB;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,SAAS,CAAC,OAAO;QACxB,SAAS,EAAE,QAAQ;KACpB;IACD,aAAa,EAAE;QACb,KAAK,EAAE,SAAS,CAAC,OAAO;KACzB;IACD,WAAW,EAAE;QACX,KAAK,EAAE,SAAS,CAAC,IAAI;KACtB;IACD,mBAAmB,EAAE;QACnB,KAAK,EAAE,SAAS,CAAC,IAAI;KACtB;IACD,UAAU,EAAE;QACV,KAAK,EAAE,SAAS,CAAC,IAAI;KACtB;IACD,eAAe,EAAE;QACf,KAAK,EAAE,SAAS,CAAC,YAAY;KAC9B;IACD,wBAAwB,EAAE;QACxB,KAAK,EAAE,SAAS,CAAC,YAAY;KAC9B;IACD,WAAW,EAAE;QACX,KAAK,EAAE,SAAS,CAAC,SAAS;KAC3B;IACD,gBAAgB,EAAE;QAChB,KAAK,EAAE,SAAS,CAAC,SAAS;KAC3B;IACD,mBAAmB,EAAE;QACnB,KAAK,EAAE,SAAS,CAAC,SAAS;KAC3B;IACD,cAAc,EAAE;QACd,KAAK,EAAE,SAAS,CAAC,YAAY;KAC9B;IACD,eAAe,EAAE;QACf,SAAS,EAAE,QAAQ;KACpB;IACD,aAAa,EAAE;QACb,UAAU,EAAE,MAAM;KACnB;IACD,aAAa,EAAE;QACb,KAAK,EAAE,SAAS,CAAC,YAAY;KAC9B;IACD,mBAAmB,EAAE;QACnB,KAAK,EAAE,SAAS,CAAC,YAAY;KAC9B;IACD,kBAAkB,EAAE;QAClB,KAAK,EAAE,SAAS,CAAC,YAAY;KAC9B;IACD,qBAAqB,EAAE;QACrB,KAAK,EAAE,SAAS,CAAC,YAAY;KAC9B;IACD,oBAAoB,EAAE;QACpB,KAAK,EAAE,SAAS,CAAC,YAAY;KAC9B;IACD,sBAAsB,EAAE;QACtB,KAAK,EAAE,SAAS,CAAC,YAAY;KAC9B;IACD,eAAe,EAAE;QACf,eAAe,EAAE,SAAS;QAC1B,OAAO,EAAE,cAAc;QACvB,KAAK,EAAE,MAAM;KACd;IACD,eAAe,EAAE;QACf,eAAe,EAAE,MAAM;QACvB,OAAO,EAAE,cAAc;QACvB,KAAK,EAAE,MAAM;KACd;CACF,EACD,SAAS,CACV,CAAC"}