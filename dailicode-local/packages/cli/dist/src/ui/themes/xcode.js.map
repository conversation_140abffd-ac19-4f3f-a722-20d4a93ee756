{"version": 3, "file": "xcode.js", "sourceRoot": "", "sources": ["../../../../src/ui/themes/xcode.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAoB,KAAK,EAAE,MAAM,YAAY,CAAC;AAErD,MAAM,WAAW,GAAgB;IAC/B,IAAI,EAAE,OAAO;IACb,UAAU,EAAE,MAAM;IAClB,UAAU,EAAE,OAAO;IACnB,SAAS,EAAE,SAAS;IACpB,UAAU,EAAE,SAAS;IACrB,YAAY,EAAE,SAAS;IACvB,UAAU,EAAE,SAAS;IACrB,WAAW,EAAE,SAAS;IACtB,YAAY,EAAE,SAAS;IACvB,SAAS,EAAE,SAAS;IACpB,OAAO,EAAE,SAAS;IAClB,IAAI,EAAE,SAAS;IACf,cAAc,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;CACvC,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAAU,IAAI,KAAK,CACnC,OAAO,EACP,OAAO,EACP;IACE,IAAI,EAAE;QACJ,OAAO,EAAE,OAAO;QAChB,SAAS,EAAE,MAAM;QACjB,OAAO,EAAE,OAAO;QAChB,UAAU,EAAE,WAAW,CAAC,UAAU;QAClC,KAAK,EAAE,WAAW,CAAC,UAAU;KAC9B;IACD,gBAAgB,EAAE;QAChB,KAAK,EAAE,WAAW,CAAC,IAAI;KACxB;IACD,cAAc,EAAE;QACd,KAAK,EAAE,WAAW,CAAC,OAAO;KAC3B;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,WAAW,CAAC,OAAO;KAC3B;IACD,UAAU,EAAE;QACV,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,gBAAgB,EAAE;QAChB,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,cAAc,EAAE;QACd,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,mBAAmB,EAAE;QACnB,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,cAAc,EAAE;QACd,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,WAAW,EAAE;QACX,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,eAAe,EAAE;QACf,KAAK,EAAE,WAAW,CAAC,UAAU;KAC9B;IACD,wBAAwB,EAAE;QACxB,KAAK,EAAE,WAAW,CAAC,UAAU;KAC9B;IACD,WAAW,EAAE;QACX,KAAK,EAAE,WAAW,CAAC,SAAS;KAC7B;IACD,aAAa,EAAE;QACb,KAAK,EAAE,WAAW,CAAC,SAAS;KAC7B;IACD,kBAAkB,EAAE;QAClB,KAAK,EAAE,WAAW,CAAC,SAAS;KAC7B;IACD,aAAa,EAAE;QACb,KAAK,EAAE,WAAW,CAAC,SAAS;KAC7B;IACD,WAAW,EAAE;QACX,KAAK,EAAE,WAAW,CAAC,SAAS;KAC7B;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,WAAW,CAAC,UAAU;KAC9B;IACD,aAAa,EAAE;QACb,KAAK,EAAE,WAAW,CAAC,UAAU;KAC9B;IACD,aAAa,EAAE;QACb,KAAK,EAAE,WAAW,CAAC,UAAU;KAC9B;IACD,aAAa,EAAE;QACb,KAAK,EAAE,WAAW,CAAC,UAAU;KAC9B;IACD,cAAc,EAAE;QACd,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,WAAW,EAAE;QACX,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,wBAAwB,EAAE;QACxB,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,WAAW,EAAE;QACX,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,eAAe,EAAE;QACf,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,mBAAmB,EAAE;QACnB,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,aAAa,EAAE;QACb,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,WAAW,EAAE;QACX,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,WAAW,CAAC,UAAU;KAC9B;IACD,cAAc,EAAE;QACd,eAAe,EAAE,MAAM;QACvB,SAAS,EAAE,QAAQ;KACpB;IACD,eAAe,EAAE;QACf,eAAe,EAAE,SAAS;KAC3B;IACD,eAAe,EAAE;QACf,eAAe,EAAE,SAAS;KAC3B;IACD,kBAAkB,EAAE;QAClB,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,qBAAqB,EAAE;QACrB,KAAK,EAAE,WAAW,CAAC,YAAY;KAChC;IACD,aAAa,EAAE;QACb,UAAU,EAAE,MAAM;KACnB;IACD,aAAa,EAAE;QACb,UAAU,EAAE,MAAM;KACnB;IACD,eAAe,EAAE;QACf,SAAS,EAAE,QAAQ;KACpB;CACF,EACD,WAAW,CACZ,CAAC"}