{"version": 3, "file": "TableRenderer.js", "sourceRoot": "", "sources": ["../../../../src/ui/utils/TableRenderer.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAQ/E;;;GAGG;AACH,MAAM,CAAC,MAAM,aAAa,GAAiC,CAAC,EAC1D,OAAO,EACP,IAAI,EACJ,aAAa,GACd,EAAE,EAAE;IACH,+EAA+E;IAC/E,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QACjD,MAAM,WAAW,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAC1B,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAC3D,CAAC;QACF,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc;IAC/D,CAAC,CAAC,CAAC;IAEH,0CAA0C;IAC1C,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3E,MAAM,WAAW,GACf,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,MAAM,cAAc,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAChD,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,CAChC,CAAC;IAEF,qDAAqD;IACrD,MAAM,UAAU,GAAG,CACjB,OAAe,EACf,KAAa,EACb,QAAQ,GAAG,KAAK,EACC,EAAE;QACnB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;QAC5C,MAAM,YAAY,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAEjD,IAAI,WAAW,GAAG,OAAO,CAAC;QAC1B,IAAI,YAAY,GAAG,YAAY,EAAE,CAAC;YAChC,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;gBACtB,mCAAmC;gBACnC,WAAW,GAAG,OAAO,CAAC,SAAS,CAC7B,CAAC,EACD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CACvC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,8DAA8D;gBAC9D,IAAI,IAAI,GAAG,CAAC,CAAC;gBACb,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC;gBAC3B,IAAI,aAAa,GAAG,OAAO,CAAC;gBAE5B,qDAAqD;gBACrD,OAAO,IAAI,IAAI,KAAK,EAAE,CAAC;oBACrB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC3C,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;oBAC5C,MAAM,cAAc,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBAErD,IAAI,cAAc,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;wBACvC,aAAa,GAAG,SAAS,CAAC;wBAC1B,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;oBACjB,CAAC;yBAAM,CAAC;wBACN,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;oBAClB,CAAC;gBACH,CAAC;gBAED,WAAW,GAAG,aAAa,GAAG,KAAK,CAAC;YACtC,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAC3D,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,GAAG,kBAAkB,CAAC,CAAC;QAErE,OAAO,CACL,MAAC,IAAI,eACF,QAAQ,CAAC,CAAC,CAAC,CACV,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,MAAM,CAAC,UAAU,YACjC,KAAC,YAAY,IAAC,IAAI,EAAE,WAAW,GAAI,GAC9B,CACR,CAAC,CAAC,CAAC,CACF,KAAC,YAAY,IAAC,IAAI,EAAE,WAAW,GAAI,CACpC,EACA,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,IACrB,CACR,CAAC;IACJ,CAAC,CAAC;IAEF,mCAAmC;IACnC,MAAM,YAAY,GAAG,CAAC,IAAiC,EAAmB,EAAE;QAC1E,MAAM,KAAK,GAAG;YACZ,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE;YAC5D,MAAM,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE;YAC/D,MAAM,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE;SAChE,CAAC;QAEF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACzE,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QAEtE,OAAO,KAAC,IAAI,cAAE,MAAM,GAAQ,CAAC;IAC/B,CAAC,CAAC;IAEF,wCAAwC;IACxC,MAAM,SAAS,GAAG,CAAC,KAAe,EAAE,QAAQ,GAAG,KAAK,EAAmB,EAAE;QACvE,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC9C,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzC,OAAO,UAAU,CAAC,IAAI,IAAI,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,OAAO,CACL,MAAC,IAAI,yBACD,GAAG,EACJ,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAClC,MAAC,KAAK,CAAC,QAAQ,eACZ,IAAI,EACJ,KAAK,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAF3B,KAAK,CAGT,CAClB,CAAC,EAAE,GAAG,cAEF,CACR,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aAEnC,YAAY,CAAC,KAAK,CAAC,EAGnB,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,EAGxB,YAAY,CAAC,QAAQ,CAAC,EAGtB,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CACxB,KAAC,KAAK,CAAC,QAAQ,cAAc,SAAS,CAAC,GAAG,CAAC,IAAtB,KAAK,CAAmC,CAC9D,CAAC,EAGD,YAAY,CAAC,QAAQ,CAAC,IACnB,CACP,CAAC;AACJ,CAAC,CAAC"}