{"version": 3, "file": "computeStats.js", "sourceRoot": "", "sources": ["../../../../src/ui/utils/computeStats.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAQH,MAAM,UAAU,kBAAkB,CAAC,OAAqB;IACtD,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;QACpC,OAAO,CAAC,CAAC;IACX,CAAC;IACD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC;AACrE,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,OAAqB;IAC3D,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;QACpC,OAAO,CAAC,CAAC;IACX,CAAC;IACD,OAAO,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;AAChE,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAC,OAAqB;IACzD,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAChC,OAAO,CAAC,CAAC;IACX,CAAC;IACD,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;AAC/D,CAAC;AAED,MAAM,CAAC,MAAM,mBAAmB,GAAG,CACjC,OAAuB,EACD,EAAE;IACxB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;IAClC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAC/C,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,cAAc,EAC9C,CAAC,CACF,CAAC;IACF,MAAM,aAAa,GAAG,KAAK,CAAC,eAAe,CAAC;IAC5C,MAAM,eAAe,GAAG,YAAY,GAAG,aAAa,CAAC;IACrD,MAAM,cAAc,GAClB,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,eAAe,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE,MAAM,eAAe,GACnB,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,eAAe,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAEpE,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CACpD,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EACzC,CAAC,CACF,CAAC;IACF,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CACpD,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EACzC,CAAC,CACF,CAAC;IACF,MAAM,eAAe,GACnB,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE5E,MAAM,cAAc,GAClB,KAAK,CAAC,cAAc,CAAC,MAAM;QAC3B,KAAK,CAAC,cAAc,CAAC,MAAM;QAC3B,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC;IAC9B,MAAM,WAAW,GACf,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,MAAM,aAAa,GACjB,cAAc,GAAG,CAAC;QAChB,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,cAAc,CAAC,GAAG,GAAG;QACtD,CAAC,CAAC,CAAC,CAAC;IAER,OAAO;QACL,YAAY;QACZ,aAAa;QACb,eAAe;QACf,cAAc;QACd,eAAe;QACf,eAAe;QACf,cAAc;QACd,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,iBAAiB;KAClB,CAAC;AACJ,CAAC,CAAC"}