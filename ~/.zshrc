# DailiCode 配置 - OpenRouter GPT-4o-mini API
export USE_CUSTOM_LLM=true
export CUSTOM_LLM_PROVIDER="openai"
export CUSTOM_LLM_API_KEY="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"
export CUSTOM_LLM_ENDPOINT="https://openrouter.ai/api/v1"
export CUSTOM_LLM_MODEL_NAME="openai/gpt-4o-mini"
export CUSTOM_LLM_TEMPERATURE=0.7
export CUSTOM_LLM_MAX_TOKENS=8192
export CUSTOM_LLM_TOP_P=1

# DailiCode 快捷命令 - 多模型支持
# GPT-4o-mini (默认，便宜快速)
alias dlc-mini='USE_CUSTOM_LLM=true CUSTOM_LLM_PROVIDER="openai" CUSTOM_LLM_API_KEY="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" CUSTOM_LLM_ENDPOINT="https://openrouter.ai/api/v1" CUSTOM_LLM_MODEL_NAME="openai/gpt-4o-mini" dlc'

# GPT-4o (更强大，稍贵)
alias dlc-4o='USE_CUSTOM_LLM=true CUSTOM_LLM_PROVIDER="openai" CUSTOM_LLM_API_KEY="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" CUSTOM_LLM_ENDPOINT="https://openrouter.ai/api/v1" CUSTOM_LLM_MODEL_NAME="openai/gpt-4o" dlc'

# Claude 3.5 Sonnet (编程专家)
alias dlc-claude='USE_CUSTOM_LLM=true CUSTOM_LLM_PROVIDER="openai" CUSTOM_LLM_API_KEY="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" CUSTOM_LLM_ENDPOINT="https://openrouter.ai/api/v1" CUSTOM_LLM_MODEL_NAME="anthropic/claude-3.5-sonnet" dlc'

# Gemini 2.0 Flash (Google最新)
alias dlc-gemini='USE_CUSTOM_LLM=true CUSTOM_LLM_PROVIDER="openai" CUSTOM_LLM_API_KEY="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" CUSTOM_LLM_ENDPOINT="https://openrouter.ai/api/v1" CUSTOM_LLM_MODEL_NAME="google/gemini-2.0-flash-001" dlc'

# 默认别名指向 mini 版本
alias dlc-ai='dlc-mini'
