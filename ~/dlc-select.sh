#!/bin/bash

# DailiCode 模型选择器
echo "🤖 DailiCode 模型选择器"
echo "========================"
echo "1. GPT-4o-mini (推荐 - 便宜快速)"
echo "2. GPT-4o (更强大)"
echo "3. Claude 3.5 Sonnet (编程专家)"
echo "4. Gemini 2.0 Flash (Google最新)"
echo "5. 退出"
echo ""

read -p "请选择模型 (1-5): " choice

case $choice in
    1)
        echo "🚀 启动 DailiCode with GPT-4o-mini..."
        USE_CUSTOM_LLM=true CUSTOM_LLM_PROVIDER="openai" CUSTOM_LLM_API_KEY="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" CUSTOM_LLM_ENDPOINT="https://openrouter.ai/api/v1" CUSTOM_LLM_MODEL_NAME="openai/gpt-4o-mini" dlc
        ;;
    2)
        echo "🚀 启动 DailiCode with GPT-4o..."
        USE_CUSTOM_LLM=true CUSTOM_LLM_PROVIDER="openai" CUSTOM_LLM_API_KEY="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" CUSTOM_LLM_ENDPOINT="https://openrouter.ai/api/v1" CUSTOM_LLM_MODEL_NAME="openai/gpt-4o" dlc
        ;;
    3)
        echo "🚀 启动 DailiCode with Claude 3.5 Sonnet..."
        USE_CUSTOM_LLM=true CUSTOM_LLM_PROVIDER="openai" CUSTOM_LLM_API_KEY="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" CUSTOM_LLM_ENDPOINT="https://openrouter.ai/api/v1" CUSTOM_LLM_MODEL_NAME="anthropic/claude-3.5-sonnet" dlc
        ;;
    4)
        echo "🚀 启动 DailiCode with Gemini 2.0 Flash..."
        USE_CUSTOM_LLM=true CUSTOM_LLM_PROVIDER="openai" CUSTOM_LLM_API_KEY="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" CUSTOM_LLM_ENDPOINT="https://openrouter.ai/api/v1" CUSTOM_LLM_MODEL_NAME="google/gemini-2.0-flash-001" dlc
        ;;
    5)
        echo "👋 再见！"
        exit 0
        ;;
    *)
        echo "❌ 无效选择，请重新运行脚本"
        exit 1
        ;;
esac
